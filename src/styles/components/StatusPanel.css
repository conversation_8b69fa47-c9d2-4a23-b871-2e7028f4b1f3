@tailwind base;
@tailwind components;
@tailwind utilities;
@layer components {
  /* Status Panel Header */
  .status-panel-header {
    @apply w-full bg-background shadow-sm z-20 border-b border-border transition-all duration-300 mb-2;
    background-image: linear-gradient(to bottom, hsl(var(--background)), hsl(var(--muted) / 0.5));
    top: 0;
  }
  /* Add subtle hover effect */
  .status-panel-header:hover .header-section-middle {
    @apply bg-muted rounded-md px-2;
  }
  /* Add subtle animation on hover */
  .status-panel-header:hover {
    @apply shadow;
  }
  .status-panel-header-content {
    @apply flex items-center justify-between px-3 py-1;
  }
  /* Sections */
  .header-section {
    @apply flex items-center;
  }
  .header-section-left {
    @apply flex-shrink-0 w-auto;
  }
  .header-section-middle {
    @apply flex-grow flex justify-center;
  }
  .header-section-right {
    @apply flex-shrink-0 w-auto justify-end;
  }
  /* Connection Status */
  .connection-status {
    @apply flex items-center justify-center;
  }
  /* Progress Bar */
  .progress-container {
    @apply relative w-40 h-3 bg-gray-100 rounded-full overflow-hidden shadow-inner border border-gray-200;
  }
  .progress-bar {
    @apply h-full rounded-full transition-all duration-300;
  }
  .progress-bar-blue {
    @apply bg-primary-500;
  }
  .progress-bar-green {
    @apply bg-green-500;
  }
  /* Status Badge */
  .status-badge {
    @apply px-3 py-1.5 text-xs font-semibold rounded-md border;
  }
  .status-badge-in-progress {
    @apply bg-primary-100 text-primary-700 border-primary-300;
  }
  .status-badge-completed {
    @apply bg-green-100 text-green-700 border-green-300;
  }
  .status-badge-other {
    @apply bg-gray-100 text-gray-700 border-gray-300;
  }
  /* Auto Navigate Toggle */
  .auto-navigate-container {
    @apply flex items-center bg-gray-50 px-2 py-1 rounded-md border border-gray-200 transition-all duration-200 hover:bg-gray-100 shadow-sm;
  }
  .auto-navigate-container:hover {
    @apply shadow-md;
  }
  .auto-navigate-label {
    @apply mr-2 text-xs font-medium text-gray-700;
  }
  .toggle-switch {
    @apply relative inline-flex items-center h-5 rounded-full w-10 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-gray-300;
  }
  .toggle-switch-knob {
    @apply inline-block w-4 h-4 transform bg-white rounded-full shadow-md transition-all duration-300 ease-in-out;
  }
  /* Add subtle glow effect when toggle is active */
  .toggle-switch[aria-pressed="true"] {
    @apply ring-1 ring-gray-300;
  }
  .toggle-switch[aria-pressed="true"] .toggle-switch-knob {
    @apply bg-white shadow-lg;
  }
  /* Action Buttons */
  .action-buttons-container {
    @apply flex bg-gray-50 rounded-md gap-1 p-1 border border-gray-200 shadow-sm transition-all duration-200;
  }
  .action-buttons-container:hover {
    @apply shadow-md bg-gray-100;
  }
  .action-button {
    @apply p-1.5 rounded hover:bg-gray-100 transition-all duration-200;
  }
  .action-button:hover {
    @apply shadow-sm transform scale-105;
  }
  .action-icon {
    @apply text-gray-500 hover:text-gray-700 transition-colors duration-200;
  }
  .action-icon-danger {
    @apply text-red-500 hover:text-red-700 transition-colors duration-200;
  }
  /* Full execution panel styles */
  #statusPanel {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    min-height: 0; /* Important for flex child to respect parent height */
    position: relative;
  }
  #statusPanel > div:not(.status-panel-header):not(.compact-status-header) {
    flex: 1;
    overflow: auto;
    min-height: 0; /* Important for nested flex containers */
    width: 100%;
  }

  /* Compact Status Header Styles */
  .compact-status-header {
    @apply w-full bg-white border-b border-gray-200 shadow-sm;
    height: 40px; /* Reduced from default height */
    min-height: 40px;
  }

  .compact-header-content {
    @apply flex items-center justify-between h-full px-3;
  }

  .compact-left-section {
    @apply flex items-center gap-3 flex-shrink-0;
  }

  .compact-progress-group {
    @apply flex items-center gap-2;
  }

  .compact-progress-bar {
    @apply w-24 h-1.5 bg-gray-200 rounded-full overflow-hidden;
  }

  .compact-progress-fill {
    @apply h-full transition-all duration-300 rounded-full;
  }

  .compact-progress-text {
    @apply text-xs font-medium text-gray-700 min-w-[32px];
  }

  .compact-status-badge {
    @apply px-2 py-0.5 text-xs font-medium rounded-full;
  }

  .compact-status-badge.status-complete {
    @apply bg-green-100 text-green-700;
  }

  .compact-status-badge.status-progress {
    @apply bg-blue-100 text-blue-700;
  }

  .compact-status-badge.status-other {
    @apply bg-semantic-gray-100 text-semantic-gray-700;
  }

  .compact-center-section {
    @apply flex items-center gap-3 flex-grow justify-center;
  }

  .compact-eta {
    @apply flex items-center gap-1.5 text-xs text-semantic-gray-600 bg-semantic-gray-50 px-2 py-1 rounded;
  }

  .compact-eta-value {
    @apply font-medium text-semantic-gray-800;
  }

  .compact-action-badge {
    @apply px-2 py-1 text-xs font-medium bg-primary-100 text-primary-700 rounded;
  }

  .compact-right-section {
    @apply flex items-center gap-2 flex-shrink-0;
  }

  .compact-action-btn {
    @apply px-2 py-1 text-xs font-medium rounded transition-colors duration-200;
  }

  .compact-view-log {
    @apply text-semantic-blue-600 hover:text-semantic-blue-800 hover:bg-semantic-blue-50;
  }

  .compact-review-btn {
    @apply bg-success text-success-foreground hover:bg-success/90;
  }

  .compact-toggle {
    @apply flex items-center gap-1.5;
  }

  .compact-toggle-label {
    @apply text-xs text-gray-600;
  }

  .compact-toggle-switch {
    @apply relative w-8 h-4 bg-gray-300 rounded-full transition-colors duration-200 cursor-pointer;
  }

  .compact-toggle-switch.active {
    @apply bg-orange-500;
  }

  .compact-toggle-knob {
    @apply absolute top-0.5 left-0.5 w-3 h-3 bg-white rounded-full transition-transform duration-200;
  }

  .compact-toggle-switch.active .compact-toggle-knob {
    @apply transform translate-x-4;
  }

  .compact-actions {
    @apply flex items-center gap-1;
  }

  .compact-icon-btn {
    @apply p-1.5 rounded hover:bg-gray-100 transition-colors duration-200 text-gray-600 hover:text-gray-800;
  }

  .compact-icon-btn.compact-danger {
    @apply text-red-500 hover:text-red-700 hover:bg-red-50;
  }
}