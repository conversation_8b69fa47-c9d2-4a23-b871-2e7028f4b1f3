.drawer-content {
  @apply bg-custom-bg-primary  shadow-xl absolute flex flex-col;

    &.vertical {
      @apply h-full;
    }

    &.horizontal {
      @apply w-full;
    }


  }

  .drawer-header {
    @apply flex items-center justify-between px-4 py-3 border-b border-semantic-gray-200;


  }

  .drawer-body {
    @apply h-full overflow-hidden;


  }

  .drawer-footer {
    @apply flex items-center justify-between py-4 px-6 border-t border-semantic-gray-200 dark:border-semantic-gray-700;


  }

  .drawer-open {
    &.drawer-lock-scroll {
      @apply overflow-hidden;
    }
  }

  .drawer-overlay {
    transition: all 0.3s ease-in-out;
    @apply bg-semantic-gray-900 bg-opacity-40 backdrop-blur-sm inset-0 fixed z-30;
    left: 4rem;
  }

  .drawer-overlay-after-open {
    opacity: 1;
    left: 4rem;
  }

  .drawer-overlay-before-close {
    opacity: 0;
  }
