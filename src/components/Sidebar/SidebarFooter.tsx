//src/components/Sidebar/SidebarFooter.tsx
"use client";
import React, { useEffect, useState, useContext, useRef } from "react";
import { useRouter } from "next/navigation";
import { fetchUnreadMessage, deleteAllNotification, fetchNotifications } from "@/utils/api";
import { AlertContext } from "../NotificationAlertService/AlertList";
import { Bell, DoorOpen } from "lucide-react"
import { IconButton } from '@/components/UIComponents/Buttons/IconButton';
import { NotificationsDrawer } from '@/components/Notification/NotificationsDrawer';
import { getLatestSubscription } from '@/utils/paymentAPI';
import Image from "next/image";
import PlanPopup from './PlanPopup';
import Cookies from 'js-cookie';
import { useUser } from '@/components/Context/UserContext';
import { useTheme } from '@/components/Context/ThemeContext';
import { decryptToken } from "@/utils/auth";


interface SidebarFooterProps {
  handleClickProfile?: () => void;
  handleClickLogout?: () => void;
  handleClickSettings?: () => void;
  setIsLogoutModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  handleDrawerToggle: (drawerName: string) => void;
  activeDrawer: string;
  theme?: 'light' | 'dark';
}
interface SubscriptionData {
  currentPlan: string;
  planCredits: number;
  organizationCost: number;
}


const SidebarFooter: React.FC<SidebarFooterProps> = ({
  handleClickProfile,
  handleClickLogout,
  handleClickSettings,
  setIsLogoutModalOpen,
  handleDrawerToggle,
  activeDrawer,
  theme: propTheme = 'light',
}) => {
  const router = useRouter();
  const [value, setValue] = useState(0);
  const [isPlanPopupOpen, setIsPlanPopupOpen] = useState(false);
  const { showAlert } = useContext(AlertContext)
  // const [tenant_id, setTenantId] = useState("default");
  const tenant_id=(Cookies.get("tenant_id") || "default")
  const containerRef = useRef<HTMLDivElement>(null);
  const { user_id } = useUser();
  const { isDarkMode } = useTheme();

  // Use theme from context, fallback to prop
  const theme = isDarkMode ? 'dark' : 'light';
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData>({
    currentPlan: 'Loading...',
    planCredits: 0,
    organizationCost: 0
  });
  const [isSubscriptionLoading, setIsSubscriptionLoading] = useState(true);



  const fetchCurrentPlan = async () => {
    setIsSubscriptionLoading(true);
    try {
      const tenantId = Cookies.get('tenant_id');
       const idToken = Cookies.get("idToken");
      const userData = decryptToken(idToken);
      const userId = userData.sub
 
      if (tenantId && userId) {
        const subscription = await getLatestSubscription(userId);

        if (subscription && subscription.price_id) {
          const cleanedCost = subscription.current_cost 
          ? parseFloat(subscription.current_cost.replace('$', '').trim())
          : 0;
          // Now we can directly use the fields from the enhanced endpoint response
          setSubscriptionData({
            currentPlan: subscription.product_name,
            planCredits: subscription.credits || 50000, //50000 is for free
            organizationCost: cleanedCost
          });
        }
        else {
          setSubscriptionData({
            currentPlan: 'Free',
            planCredits: 50000,
            organizationCost: 0
          });
        }
      }
    } catch (error) {
      
      setSubscriptionData({
        currentPlan: 'Free',
        planCredits: 50000,
        organizationCost: 0
      });
      // showAlert("There was an error loading your subscription data. Defaulting to Free plan.", "error");
    } finally {
      setIsSubscriptionLoading(false);
    }
  };

  const handlePlanPopupToggle =async  (e: React.MouseEvent) => {
    e.stopPropagation();
    await fetchCurrentPlan()
    setIsPlanPopupOpen(!isPlanPopupOpen);
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetchUnreadMessage();
        setValue(response);
      } catch (err) {
        
      }
    };
    fetchData();
    const intervalId = setInterval(fetchData, 600000);
    return () => clearInterval(intervalId);
  }, []);

  const handleValueChange = (values: number) => {
    setValue(values);
  };

  const handleClearAll = async () => {
    const notifications = await fetchNotifications()
    if (notifications.length === 0) {
      showAlert("No Notifications to delete", "info");
      return;
    }
    try {
      const response = await deleteAllNotification();
      showAlert("All Messsages are Deleted Successfully", "success")
      sessionStorage.removeItem('notifications')
      handleDrawerToggle("Notifications")
    } catch (err) {
      showAlert("Error in Deleting all Messages", "danger")
    }
  }


  return (
    <div className="sidebar-footer-container relative">
      <IconButton
        icon={<Bell size={24} className="sidebar-footer-container-icon"/>}
        tooltip="Notifications"
        onClick={() => handleDrawerToggle("Notifications")}
        badgeContent={value}
        theme={theme}
      />
      {/* <IconButton
        icon={<Settings size={24} className="sidebar-footer-container-icon" />}
        tooltip="Settings"
        onClick={() => router.push("/users/settings/profile")}
      /> */}
      {(String(tenant_id).toLowerCase().startsWith("default") || String(tenant_id) === process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID) && (
        <div className="relative" ref={containerRef}>
          <button
            className="flex items-center justify-center w-10 h-10 border-2 border-orange-400 rounded-xl bg-gradient-to-b from-orange-500 to-orange-800 hover:opacity-90 transition-opacity"
            onClick={handlePlanPopupToggle}
            title="Upgrade Plan"
          >
            <Image
              src="/logo/kavia_logo_white.svg"
              alt="Upgrade Plan"
              width={18}
              height={18}
            />
          </button>
          <PlanPopup
            isOpen={isPlanPopupOpen}
            onClose={() => setIsPlanPopupOpen(false)}
            credits={1000}
            isSubscriptionLoading={isSubscriptionLoading}
            subscriptionData={subscriptionData}
            containerRef={containerRef}
          />
        </div>
      )}
      <IconButton
        icon={<DoorOpen size={24} className="sidebar-footer-container-logout-icon"  />}
        tooltip="Logout"
        onClick={() => setIsLogoutModalOpen(true)}
      />
      <NotificationsDrawer
        isOpen={activeDrawer === "Notifications"}
        onClose={() => handleDrawerToggle("Notifications")}
        onClearAll={handleClearAll}
        handleValueChange={setValue}
        theme={theme}
      />

    </div >
  );
};

export default SidebarFooter;
