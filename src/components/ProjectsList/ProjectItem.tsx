import React, { useState, useRef, useEffect } from 'react';
import { ArrowUpRight, MoreVertical, Edit, Trash2, Copy } from 'lucide-react';
import { formatListDate } from "@/utils/datetime";


interface ProjectItemProps {
  project: any;
  isSelected: boolean;
  onClick: () => void;
  onEditTitle?: (project: any) => void;
  onDeleteProject?: (project: any) => void;
  onCloneProject?: (project: any) => void;
  isSharedProject?: boolean;
  theme?: 'light' | 'dark';
}

const ProjectItem: React.FC<ProjectItemProps> = ({ project,
  isSelected,
  onClick,
  onEditTitle,
  onDeleteProject,
  onCloneProject,
  isSharedProject = false,
  theme = 'light'}) => {

    const [menuOpen, setMenuOpen] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0 });
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Theme classes
  const themeClasses = {
    light: {
      container: "project-hover-container flex items-center justify-between px-4 py-3 border-b border-custom-border cursor-pointer w-full relative bg-custom-bg-primary hover:bg-custom-bg-secondary group",
      selectedContainer: "project-hover-container flex items-center justify-between px-4 py-3 border-b border-custom-border cursor-pointer w-full relative bg-custom-bg-secondary hover:bg-custom-bg-secondary group",
      title: "typography-body-sm font-weight-medium text-custom-text-primary truncate",
      metadata: "typography-caption text-custom-text-secondary mt-1",
      creatorName: "truncate text-custom-text-secondary",
      createdDate: "whitespace-nowrap text-custom-text-secondary",
      arrowIcon: "text-custom-text-secondary group-hover:text-primary",
      menuButton: "w-5 h-5 rounded-full hover:bg-custom-bg-secondary flex items-center justify-center",
      menuIcon: "text-custom-text-secondary",
      dropdown: "fixed w-48 bg-custom-bg-primary rounded-md shadow-lg z-50 py-1 border border-custom-border",
      menuItem: "flex items-center w-full px-4 py-2 typography-body-sm text-custom-text-primary hover:bg-custom-bg-secondary",
      deleteMenuItem: "flex items-center w-full px-4 py-2 typography-body-sm text-destructive hover:bg-custom-bg-secondary"
    },
    dark: {
      container: "project-hover-container flex items-center justify-between px-4 py-3 border-b border-custom-border cursor-pointer w-full relative bg-custom-bg-primary hover:bg-custom-bg-secondary group",
      selectedContainer: "project-hover-container flex items-center justify-between px-4 py-3 border-b border-custom-border cursor-pointer w-full relative bg-custom-bg-secondary hover:bg-custom-bg-secondary group",
      title: "typography-body-sm font-weight-medium text-custom-text-primary truncate",
      metadata: "typography-caption text-custom-text-secondary mt-1",
      creatorName: "truncate text-custom-text-secondary",
      createdDate: "whitespace-nowrap text-custom-text-secondary",
      arrowIcon: "text-custom-text-secondary group-hover:text-primary",
      menuButton: "w-5 h-5 rounded-full hover:bg-custom-bg-secondary flex items-center justify-center",
      menuIcon: "text-custom-text-secondary",
      dropdown: "fixed w-48 bg-custom-bg-primary rounded-md shadow-lg z-50 py-1 border border-custom-border",
      menuItem: "flex items-center w-full px-4 py-2 typography-body-sm text-custom-text-primary hover:bg-custom-bg-secondary",
      deleteMenuItem: "flex items-center w-full px-4 py-2 typography-body-sm text-destructive hover:bg-custom-bg-secondary"
    }
  };

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node) &&
          buttonRef.current && !buttonRef.current.contains(event.target as Node)) {
        setMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleMenuClick = (e: React.MouseEvent) => {
    e.stopPropagation();

    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      // Position to the left of the button, aligned with the top
      setMenuPosition({
        top: rect.top,
        left: Math.max(rect.left - 160, 10) // Menu width is 192px (w-48), subtract to place on left, with minimum margin
      });
    }

    setMenuOpen(!menuOpen);
  };

  const handleEditTitle = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onEditTitle) {
      onEditTitle(project);
    }
    setMenuOpen(false);
  };

  const handleDeleteProject = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onDeleteProject) {
      onDeleteProject(project);
    }
    setMenuOpen(false);
  };

  const handleCloneProject = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onCloneProject) {
      onCloneProject(project);
    }
    setMenuOpen(false);
  };

  return (
<div
      className={isSelected ? themeClasses[theme].selectedContainer : themeClasses[theme].container}
      onClick={onClick}
    >
   <div className="flex flex-col pr-20 w-full">
      <div className={themeClasses[theme].title}>
          {project.Title}
        </div>
        <div className={themeClasses[theme].metadata}>
          <span className={themeClasses[theme].creatorName}>{project?.creator_name}, </span>
          <span className={themeClasses[theme].createdDate}>{formatListDate(project?.created_at)}</span>
        </div>
      </div>

      {/* Fixed position icons */}
      <div className="absolute right-4 top-0 bottom-0 flex items-center gap-3 pointer-events-none">
        <div className="w-5 h-5 flex items-center justify-center pointer-events-auto">
          <ArrowUpRight
            size={16}
            className={themeClasses[theme].arrowIcon}
          />
        </div>

        <div className="w-5 h-5 flex items-center justify-center pointer-events-auto" ref={menuRef}>
          <button
            ref={buttonRef}
            onClick={handleMenuClick}
            className={themeClasses[theme].menuButton}
          >
            <MoreVertical size={16} className={themeClasses[theme].menuIcon} />
          </button>

          {menuOpen && (
            <div
              ref={menuRef}
              className={themeClasses[theme].dropdown}
              style={{
                top: `${menuPosition.top}px`,
                left: `${menuPosition.left}px`
              }}
            >
              {!isSharedProject && (
                <button
                  onClick={handleEditTitle}
                  className={themeClasses[theme].menuItem}
                >
                  <Edit size={14} className="mr-2" />
                  Edit Title
                </button>
              )}
                  <button
                onClick={handleCloneProject}
                className={themeClasses[theme].menuItem}
              >
                <Copy size={14} className="mr-2" />
                Clone Project
              </button>
              {!isSharedProject && (
                <button
                  onClick={handleDeleteProject}
                  className={themeClasses[theme].deleteMenuItem}
                >
                  <Trash2 size={14} className="mr-2" />
                  Delete Project
                </button>
              )}


            </div>
          )}
        </div>
      </div>
    </div>
  );
};

ProjectItem.displayName = 'ProjectItem';

export default ProjectItem;