import React, { useState, useEffect, useContext, useMemo } from 'react';
import { Scrollbars } from 'react-custom-scrollbars-2';
import { TopBarContext } from '../Context/TopBarContext';
import { AlertContext } from '../NotificationAlertService/AlertList';
import { Plus, Upload, Filter, X, ChevronLeft, ChevronRight } from 'lucide-react';

import ProjectItem from './ProjectItem';
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import { EditTitleModal, DeleteProjectModal, CloneProjectModal } from './ProjectActionModals';
import { updateProjectTitle, deleteProject, cloneProject } from '../../utils/projectApi';
import ImportProjectModal from './ImportProjectModal';

export interface ProjectListProps {
  onProjectClick?: (project: any) => void;
  selectedProjectId?: string;
  closeSidebar?: () => void;
  openProjectModal?: () => void;
  isSharedTab?: boolean;
  refreshProjectsList?: () => void;
  theme?: 'light' | 'dark';
  onDrawerClose: () => void;
  
  // New props for server-side pagination (private projects)
  projectsData?: any[];
  pagination?: any;
  creators?: string[];
  onPageChange?: (page: number) => void;
  searchTerm?: string;
  onSearchChange?: (term: string) => void;
  selectedCreator?: string;
  onCreatorChange?: (creator: string) => void;
  isSearching?: boolean;
  loading?: boolean;
  
  // Legacy prop for shared projects (client-side)
  projects?: any[];
}

const ProjectList: React.FC<ProjectListProps> = ({
  onProjectClick,
  selectedProjectId: propSelectedProjectId,
  closeSidebar,
  openProjectModal,
  onDrawerClose,
  isSharedTab = false,
  refreshProjectsList,
  theme = 'light',
  projectsData = [],
  pagination = null,
  creators = [],
  onPageChange,
  searchTerm = '',
  onSearchChange,
  selectedCreator = '',
  onCreatorChange,
  loading = false,
  isSearching = false,
  projects = [] // Legacy prop for shared projects
}) => {

  // State management for private projects (server-side)
  const [selectedProjectId, setSelectedProjectId] = useState<string>(() => {
    return sessionStorage.getItem('selectedProjectId') || propSelectedProjectId || '';
  });

  // State management for shared projects (client-side)
  const [sharedSearchTerm, setSharedSearchTerm] = useState("");
  const [sharedSelectedCreator, setSharedSelectedCreator] = useState<string>('');
  const [sharedCurrentPage, setSharedCurrentPage] = useState(1);
  const [sharedItemsPerPage] = useState(10);
  
  // Common state
  const [activeFilters, setActiveFilters] = useState<string[]>([]);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [creatorDropdownOpen, setCreatorDropdownOpen] = useState(false);

  // State for modals
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [cloneModalOpen, setCloneModalOpen] = useState(false);
  const [importModalOpen, setImportModalOpen] = useState(false);
  const [isCloning, setIsCloning] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [selectedProject, setSelectedProject] = useState<any>(null);

  const { getActiveTab } = useContext(TopBarContext);
  const { showAlert } = useContext(AlertContext);

  // Available filters for shared projects
  const availableFilters = [
    { id: 'rdk', label: 'RDK Projects', searchTerm: 'rdk', icon: '🔧' },
    { id: 'sample', label: 'Sample', searchTerm: 'sample', icon: '🧪' }
  ];

  // Get unique creators for shared projects (client-side)
  const sharedUniqueCreators = useMemo(() => {
    if (!isSharedTab) return [];
    
    const creators = projects
      .map(project => project.creator_name)
      .filter((name, index, self) => name && self.indexOf(name) === index)
      .sort();
    return creators;
  }, [projects, isSharedTab]);

  // Theme classes
  const themeClasses = {
    light: {
      container: "chat-list flex flex-col h-full w-full bg-custom-bg-primary",
      searchContainer: "p-4 border-b border-semantic-gray-200 bg-custom-bg-primary relative",
      searchInput: "w-full h-[38px] pl-12 pr-12 rounded-lg border border-semantic-gray-200 bg-custom-bg-primary text-semantic-gray-900 placeholder-semantic-gray-400 text-base focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",
      searchIcon: "text-semantic-gray-400",
      filterButton: "absolute right-3 top-1/2 -translate-y-1/2 p-1.5 rounded transition-all hover:bg-semantic-gray-100 text-semantic-gray-500 hover:text-semantic-gray-700",
      listContainer: "chat-list-container bg-custom-bg-primary min-h-full",
      createButtonContainer: "px-3 py-4 bg-custom-bg-primary border-t border-semantic-gray-200 create-project-button-container",
      createButton: "flex-1 py-2 bg-primary hover:bg-primary-600 text-primary-foreground text-base font-medium rounded flex items-center justify-center",
      importButton: "flex-1 py-2 border border-semantic-gray-300 bg-custom-bg-primary hover:bg-semantic-gray-50 text-semantic-gray-700 text-base font-medium rounded flex items-center justify-center",
      scrollThumb: "hsl(var(--semantic-gray-900) / 0.2)",
      scrollTrack: "transparent",
      paginationContainer: "px-3 py-4 bg-custom-bg-primary border-t border-semantic-gray-200 flex items-center justify-between min-h-[46px] shadow-sm",
      paginationButton: "px-3 py-2 rounded border border-semantic-gray-300 bg-custom-bg-primary hover:bg-semantic-gray-50 text-semantic-gray-700 disabled:opacity-50 disabled:cursor-not-allowed min-w-[40px] flex items-center justify-center",
      paginationInfo: "text-sm text-semantic-gray-600 font-medium",
      dropdown: "absolute right-0 top-full mt-1 w-48 rounded-md shadow-lg overflow-hidden z-50 bg-custom-bg-primary border border-semantic-gray-200",
      dropdownItem: "w-full px-3 py-2 text-left text-sm flex items-center justify-between hover:bg-semantic-gray-50 text-semantic-gray-700",
      loadingContainer: "flex items-center justify-center p-8 text-semantic-gray-500",
    },
    dark: {
      container: "chat-list flex flex-col h-full w-full bg-custom-bg-primary",
      searchContainer: "p-4 border-b border-semantic-gray-700 bg-custom-bg-primary relative",
      searchInput: "w-full h-[38px] pl-12 pr-12 rounded-lg border border-semantic-gray-700 bg-semantic-gray-800 text-semantic-gray-100 placeholder-semantic-gray-400 text-base focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",
      searchIcon: "text-semantic-gray-400",
      filterButton: "absolute right-3 top-1/2 -translate-y-1/2 p-1.5 rounded transition-all hover:bg-semantic-gray-700 text-semantic-gray-400 hover:text-semantic-gray-200",
      listContainer: "chat-list-container bg-custom-bg-primary min-h-full",
      createButtonContainer: "px-3 py-4 bg-custom-bg-primary border-t border-semantic-gray-700 create-project-button-container",
      createButton: "flex-1 py-2 bg-primary hover:bg-primary-700 text-primary-foreground text-base font-medium rounded flex items-center justify-center",
      importButton: "flex-1 py-2 border border-semantic-gray-600 bg-semantic-gray-800 hover:bg-semantic-gray-700 text-semantic-gray-100 text-base font-medium rounded flex items-center justify-center",
      scrollThumb: "hsl(var(--semantic-gray-100) / 0.2)",
      scrollTrack: "transparent",
      paginationContainer: "px-3 py-4 bg-custom-bg-primary border-t border-semantic-gray-700 flex items-center justify-between min-h-[46px] shadow-sm",
      paginationButton: "px-3 py-2 rounded border border-semantic-gray-600 bg-semantic-gray-800 hover:bg-semantic-gray-700 text-semantic-gray-100 disabled:opacity-50 disabled:cursor-not-allowed min-w-[40px] flex items-center justify-center",
      paginationInfo: "text-sm text-semantic-gray-400 font-medium",
      dropdown: "absolute right-0 top-full mt-1 w-48 rounded-md shadow-lg overflow-hidden z-50 bg-semantic-gray-800 border border-semantic-gray-700",
      dropdownItem: "w-full px-3 py-2 text-left text-sm flex items-center justify-between hover:bg-semantic-gray-700 text-semantic-gray-200",
      loadingContainer: "flex items-center justify-center p-8 text-semantic-gray-400",
    }
  };

  // Get current search term and creator based on tab
  const currentSearchTerm = isSharedTab ? sharedSearchTerm : searchTerm;
  const currentSelectedCreator = isSharedTab ? sharedSelectedCreator : selectedCreator;
  const currentCreators = isSharedTab ? sharedUniqueCreators : creators;

  // Filter projects for shared tab (client-side filtering)
  const filteredSharedProjects = useMemo(() => {
    if (!isSharedTab) return [];
    
    return projects.filter(project => {
      const matchesSearch = project.Title?.toLowerCase().includes(sharedSearchTerm.toLowerCase());
      const matchesCreator = !sharedSelectedCreator || project.creator_name === sharedSelectedCreator;
      
      let matchesFilters = true;
      if (activeFilters.length > 0) {
        matchesFilters = activeFilters.some(filterId => {
          const filter = availableFilters.find(f => f.id === filterId);
          return filter && project.Title?.toLowerCase().includes(filter.searchTerm.toLowerCase());
        });
      }

      return matchesSearch && matchesCreator && matchesFilters;
    });
  }, [projects, isSharedTab, sharedSearchTerm, sharedSelectedCreator, activeFilters]);

  // Pagination logic for shared projects
  const sharedTotalPages = Math.ceil(filteredSharedProjects.length / sharedItemsPerPage);
  const sharedStartIndex = (sharedCurrentPage - 1) * sharedItemsPerPage;
  const sharedEndIndex = sharedStartIndex + sharedItemsPerPage;
  const currentSharedProjects = filteredSharedProjects.slice(sharedStartIndex, sharedEndIndex);

  // Get current projects to display
  const currentProjects = isSharedTab ? currentSharedProjects : projectsData;

  // Reset shared pagination when filters change
  useEffect(() => {
    if (isSharedTab) {
      setSharedCurrentPage(1);
    }
  }, [sharedSearchTerm, sharedSelectedCreator, activeFilters, isSharedTab]);

  // Handle project click
  const handleProjectClick = (project: any) => {
    onProjectClick?.(project);
    setSelectedProjectId(project.id);
    sessionStorage.setItem('selectedProjectId', project.id);
    closeSidebar?.();
  };

  // Handle search change
  const handleSearchChange = (value: string) => {
    if (isSharedTab) {
      setSharedSearchTerm(value);
    } else {
      onSearchChange?.(value);
    }
  };

  // Handle creator change
  const handleCreatorChange = (creator: string) => {
    if (isSharedTab) {
      setSharedSelectedCreator(creator);
    } else {
      onCreatorChange?.(creator);
    }
  };

  // Handle clear search
  const handleClearSearch = () => {
    if (isSharedTab) {
      setSharedSearchTerm("");
      setSharedSelectedCreator("");
    } else {
      onSearchChange?.("");
      onCreatorChange?.("");
    }
    setActiveFilters([]);
  };

  // Handle creator filter
  const handleCreatorFilter = (creatorName: string) => {
    const newCreator = creatorName === currentSelectedCreator ? '' : creatorName;
    handleCreatorChange(newCreator);
    setCreatorDropdownOpen(false);
  };

  // Handle shared project filters
  const toggleFilter = (filterId: string) => {
    setActiveFilters(prev =>
      prev.includes(filterId)
        ? prev.filter(f => f !== filterId)
        : [...prev, filterId]
    );
    setDropdownOpen(false);
  };

  // Pagination handlers
  const handlePrevious = () => {
    if (isSharedTab) {
      if (sharedCurrentPage > 1) {
        setSharedCurrentPage(sharedCurrentPage - 1);
      }
    } else {
      if (pagination?.has_previous) {
        onPageChange?.(pagination.current_page - 1);
      }
    }
  };

  const handleNext = () => {
    if (isSharedTab) {
      if (sharedCurrentPage < sharedTotalPages) {
        setSharedCurrentPage(sharedCurrentPage + 1);
      }
    } else {
      if (pagination?.has_next) {
        onPageChange?.(pagination.current_page + 1);
      }
    }
  };

  const createNewProject = () => {
    openProjectModal?.();
    closeSidebar?.();
  };

  const handleImportProject = () => {
    setImportModalOpen(true);
  };

  // Project action handlers
  const handleEditTitle = (project: any) => {
    setSelectedProject(project);
    setEditModalOpen(true);
  };

  const handleDeleteProject = (project: any) => {
    setSelectedProject(project);
    setDeleteModalOpen(true);
  };

  const handleCloneProject = (project: any) => {
    setSelectedProject(project);
    setCloneModalOpen(true);
  };

  // API action handlers
  const handleSaveTitle = async (projectId: string, newTitle: string) => {
    setIsEditing(true);
    try {
      const response = await updateProjectTitle(projectId, newTitle);
      if (response) {
        setEditModalOpen(false);
        showAlert(`Project title updated to "${newTitle}"`, 'success');
        refreshProjectsList?.();
      }
    } catch (error) {
      showAlert('Failed to update project title', 'error');
    } finally {
      setIsEditing(false);
    }
  };

  const handleConfirmDelete = async (projectId: string) => {
    setIsDeleting(true);
    try {
      const response = await deleteProject(projectId);
      if (response) {
        setDeleteModalOpen(false);
        showAlert('Project deleted successfully', 'success');
        refreshProjectsList?.();
      }
    } catch (error) {
      console.error('Error deleting project:', error);
      showAlert('Failed to delete project', 'error');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleConfirmClone = async (projectId: string, newTitle: string) => {
    setIsCloning(true);
    try {
      const clonedProject = await cloneProject(projectId, newTitle);
      if (clonedProject) {
        setCloneModalOpen(false);
        showAlert(`Project "${newTitle}" cloned successfully`, 'success');
        refreshProjectsList?.();
      }
    } catch (error) {
      console.error('Error cloning project:', error);
      showAlert('Failed to clone project', 'error');
    } finally {
      setIsCloning(false);
    }
  };

  // Close dropdown handlers
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      const filterDropdown = document.querySelector('.filter-dropdown-container');
      const filterButton = document.querySelector('[aria-label="filter"]');
      if (filterDropdown && !filterDropdown.contains(target) && filterButton && !filterButton.contains(target)) {
        setDropdownOpen(false);
      }
      const creatorDropdown = document.querySelector('.creator-dropdown-container');
      const creatorButton = document.querySelector('[aria-label="creator-filter"]');
      if (creatorDropdown && !creatorDropdown.contains(target) && creatorButton && !creatorButton.contains(target)) {
        setCreatorDropdownOpen(false);
      }
    };

    if (dropdownOpen || creatorDropdownOpen) {
      setTimeout(() => {
        document.addEventListener('click', handleClickOutside);
      }, 0);
      return () => {
        document.removeEventListener('click', handleClickOutside);
      };
    }
  }, [dropdownOpen, creatorDropdownOpen]);

  // Check if we should show pagination
  const shouldShowPagination = isSharedTab 
    ? filteredSharedProjects.length > sharedItemsPerPage
    : pagination && pagination.total_pages > 1;

  // Get pagination info
  const getPaginationInfo = () => {
    if (isSharedTab) {
      return {
        startIndex: Math.min(sharedStartIndex + 1, filteredSharedProjects.length),
        endIndex: Math.min(sharedEndIndex, filteredSharedProjects.length),
        total: filteredSharedProjects.length,
        currentPage: sharedCurrentPage,
        totalPages: sharedTotalPages,
        hasPrevious: sharedCurrentPage > 1,
        hasNext: sharedCurrentPage < sharedTotalPages
      };
    } else {
      return {
        startIndex: pagination?.start_index || 0,
        endIndex: pagination?.end_index || 0,
        total: pagination?.total_count || 0,
        currentPage: pagination?.current_page || 1,
        totalPages: pagination?.total_pages || 1,
        hasPrevious: pagination?.has_previous || false,
        hasNext: pagination?.has_next || false
      };
    }
  };

  const handleClose = (closeDrawer : boolean = false) => {
    setImportModalOpen(false);
    if(closeDrawer){
      onDrawerClose();
    }
  }

  const paginationInfo = getPaginationInfo();

  return (
    <div className={themeClasses[theme].container}>
      {/* Search Container */}
      <div className={themeClasses[theme].searchContainer}>
        <div className="relative">
          <input
            type="text"
            value={currentSearchTerm}
            onChange={(e) => handleSearchChange(e.target.value)}
            placeholder="Search"
            className={`${themeClasses[theme].searchInput} ${isSharedTab ? 'pr-12' : 'pr-4'}`}
          />
          <div className="absolute left-4 top-1/2 -translate-y-1/2">
           {(!isSharedTab && isSearching) ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            ) : (
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" className={themeClasses[theme].searchIcon}>
                <circle cx="7" cy="7" r="6" stroke="currentColor" strokeWidth="1.5" />
                <path d="M11 11L14.5 14.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
              </svg>
            )}
          </div>

          {/* Filter Button */}
          <button
            onClick={() => {
              if (isSharedTab) {
                setDropdownOpen(!dropdownOpen);
              } else {
                setCreatorDropdownOpen(!creatorDropdownOpen);
              }
            }}
            aria-label={isSharedTab ? "filter" : "creator-filter"}
            className={`${themeClasses[theme].filterButton} ${(activeFilters.length > 0 || currentSelectedCreator) ? 'text-primary' : ''}`}
            title={isSharedTab ? "filter" : "Filter by users"}
          >
            <Filter size={16} />
          </button>
        </div>

        {/* Filter Dropdowns */}
        {isSharedTab && dropdownOpen && (
          <div className={`filter-dropdown-container ${themeClasses[theme].dropdown}`}>
            {availableFilters.map(filter => (
              <button
                key={filter.id}
                onClick={() => toggleFilter(filter.id)}
                className={`${themeClasses[theme].dropdownItem} ${activeFilters.includes(filter.id) ? 'bg-opacity-50' : ''}`}
              >
                <span className="text-xs">{filter.label}</span>
                {activeFilters.includes(filter.id) && (
                  <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M2 6L4 8L10 2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                )}
              </button>
            ))}
          </div>
        )}

        {/* Creator Filter Dropdown */}
        {creatorDropdownOpen && currentCreators.length > 0 && (
          <div className={`creator-dropdown-container ${themeClasses[theme].dropdown}`} style={{ maxHeight: '400px' }}>
            <div className="max-h-[400px] overflow-y-auto">
              <div className="sticky top-0 z-10" style={{
                backgroundColor: theme === 'dark' ? 'hsl(var(--semantic-gray-800))' : 'hsl(var(--background))',
                borderBottom: theme === 'dark' ? '1px solid hsl(var(--semantic-gray-700))' : '1px solid hsl(var(--semantic-gray-200))'
              }}>
                <button
                  onClick={() => handleCreatorFilter('')}
                  className={`${themeClasses[theme].dropdownItem} ${!currentSelectedCreator ? 'font-medium' : ''}`}
                >
                  <span className="text-xs">All Users</span>
                  {!currentSelectedCreator && (
                    <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M2 6L4 8L10 2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  )}
                </button>
              </div>
              <div className="py-1">
                {currentCreators.map((creator) => (
                  <button
                    key={creator}
                    onClick={() => handleCreatorFilter(creator)}
                    className={`${themeClasses[theme].dropdownItem} ${currentSelectedCreator === creator ? 'font-medium' : ''}`}
                    title={creator}
                  >
                    <span className="text-xs truncate max-w-[140px]">{creator}</span>
                    {currentSelectedCreator === creator && (
                      <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M2 6L4 8L10 2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                      </svg>
                    )}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Selected Filter Chips */}
        {((isSharedTab && activeFilters.length > 0) || currentSelectedCreator) && (
          <div className="mt-1.5 flex items-center gap-1 flex-wrap">
            {isSharedTab && activeFilters.map(filterId => {
              const filter = availableFilters.find(f => f.id === filterId);
              return filter ? (
                <div
                  key={filterId}
                  className={`inline-flex items-center gap-0.5 px-1.5 py-0.5 rounded-full text-xs ${theme === 'dark' ? 'bg-semantic-gray-700/50 text-semantic-gray-300 border border-semantic-gray-600/50' : 'bg-semantic-gray-100 text-semantic-gray-600 border border-semantic-gray-200'}`}
                >
                  <span className="text-[11px]">{filter.label}</span>
                  <button
                    onClick={() => toggleFilter(filterId)}
                    className={`p-0.5 rounded-full transition-all ${theme === 'dark' ? 'hover:bg-semantic-gray-600 text-semantic-gray-400 hover:text-semantic-gray-200' : 'hover:bg-semantic-gray-200 text-semantic-gray-500 hover:text-semantic-gray-700'}`}
                  >
                    <X size={8} />
                  </button>
                </div>
              ) : null;
            })}

            {currentSelectedCreator && (
              <div
                className={`inline-flex items-center gap-0.5 px-1.5 py-0.5 rounded-full text-xs ${theme === 'dark' ? 'bg-semantic-gray-700/50 text-semantic-gray-300 border border-semantic-gray-600/50' : 'bg-semantic-gray-100 text-semantic-gray-600 border border-semantic-gray-200'}`}
              >
                <span className="text-[11px]">{currentSelectedCreator}</span>
                <button
                  onClick={() => handleCreatorChange('')}
                  className={`p-0.5 rounded-full transition-all ${theme === 'dark' ? 'hover:bg-semantic-gray-600 text-semantic-gray-400 hover:text-semantic-gray-200' : 'hover:bg-semantic-gray-200 text-semantic-gray-500 hover:text-semantic-gray-700'}`}
                >
                  <X size={8} />
                </button>
              </div>
            )}

            <button
              onClick={handleClearSearch}
              className={`text-[11px] font-medium transition-all px-1 h-5 flex items-center ${theme === 'dark' ? 'text-semantic-gray-500 hover:text-semantic-gray-300' : 'text-semantic-gray-500 hover:text-semantic-gray-700'}`}
            >
              Clear all
            </button>
          </div>
        )}
      </div>

      {/* Projects List */}
      <Scrollbars
        style={{
          height: 'calc(100% - 120px)',
          backgroundColor: 'hsl(var(--background))'
        }}
        autoHide
        autoHideTimeout={1000}
        autoHideDuration={200}
        hideTracksWhenNotNeeded
        renderThumbVertical={({ style, ...props }) => (
          <div
            {...props}
            style={{
              ...style,
              backgroundColor: themeClasses[theme].scrollThumb,
              borderRadius: '4px',
              width: '6px',
            }}
          />
        )}
        renderTrackVertical={({ style, ...props }) => (
          <div
            {...props}
            style={{
              ...style,
              position: 'absolute',
              width: '6px',
              right: '2px',
              bottom: '2px',
              top: '2px',
              borderRadius: '3px',
            }}
          />
        )}
        renderView={({ style, ...props }) => (
          <div
            {...props}
            style={{
              ...style,
              backgroundColor: 'hsl(var(--background))',
              minHeight: '100%',
              paddingBottom: '40px'
            }}
          />
        )}
      >
        <div id='sidebar-project-list' className={themeClasses[theme].listContainer}>
          {(!isSharedTab && loading) ? (
            <div className={themeClasses[theme].loadingContainer}>
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-3">Loading projects...</span>
            </div>
          ) : currentProjects.length === 0 ? (
            <EmptyStateView 
              type={currentSearchTerm || currentSelectedCreator || activeFilters.length > 0 ? "noSearchResult" : "projects"} 
              onClick={currentSearchTerm || currentSelectedCreator || activeFilters.length > 0 ? handleClearSearch : createNewProject} 
              theme={theme} 
            />
          ) : (
            currentProjects.map((project, index) => (
              <ProjectItem
                key={project.id || index}
                project={project}
                isSelected={selectedProjectId === project.id}
                onClick={() => handleProjectClick(project)}
                onEditTitle={handleEditTitle}
                onDeleteProject={handleDeleteProject}
                onCloneProject={handleCloneProject}
                isSharedProject={isSharedTab || project.isPublic}
                theme={theme}
              />
            ))
          )}
        </div>
      </Scrollbars>

      {/* Bottom Container with Pagination and Create Buttons */}
      <div className={themeClasses[theme].createButtonContainer}>
        {/* Pagination Controls */}
        {shouldShowPagination && (
          <div className="flex items-center justify-between mb-4 pb-3 border-b border-semantic-gray-200 dark:border-semantic-gray-700">
            <div className={themeClasses[theme].paginationInfo}>
              Showing {paginationInfo.startIndex}-{paginationInfo.endIndex} of {paginationInfo.total}
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={handlePrevious}
                disabled={!paginationInfo.hasPrevious || (!isSharedTab && loading)}
                className={themeClasses[theme].paginationButton}
                title="Previous page"
              >
                <ChevronLeft size={16} />
              </button>
              <span className={`${themeClasses[theme].paginationInfo} min-w-[60px] text-center`}>
                Page {paginationInfo.currentPage} of {paginationInfo.totalPages}
              </span>
              <button
                onClick={handleNext}
                disabled={!paginationInfo.hasNext || (!isSharedTab && loading)}
                className={themeClasses[theme].paginationButton}
                title="Next page"
              >
                <ChevronRight size={16} />
              </button>
            </div>
          </div>
        )}

        {/* Create/Import Buttons */}
        {!isSharedTab && (
          <div className="flex gap-2">
            <button
              onClick={createNewProject}
              className={themeClasses[theme].createButton}
              disabled={loading}
            >
              <Plus size={16} className="mr-1" />
              New
            </button>
            <button
              className={themeClasses[theme].importButton}
              onClick={handleImportProject}
              disabled={loading}
            >
              <Upload size={16} className="mr-1" />
              Import
            </button>
          </div>
        )}
      </div>

      {/* Modals */}
      {selectedProject && (
        <>
          <EditTitleModal
            isOpen={editModalOpen}
            onClose={() => setEditModalOpen(false)}
            project={selectedProject}
            onSave={handleSaveTitle}
            isLoading={isEditing}
            isDarkMode={theme === 'dark'}
          />

          <DeleteProjectModal
            isOpen={deleteModalOpen}
            onClose={() => setDeleteModalOpen(false)}
            project={selectedProject}
            onConfirm={handleConfirmDelete}
            isLoading={isDeleting}
          />

          <CloneProjectModal
            isOpen={cloneModalOpen}
            onClose={() => setCloneModalOpen(false)}
            project={selectedProject}
            onConfirm={handleConfirmClone}
            isLoading={isCloning}
          />
        </>
      )}

      <ImportProjectModal
        isOpen={importModalOpen}
        onClose={handleClose}
        refreshProjectsList={refreshProjectsList}
      />
    </div>
  );
};

ProjectList.displayName = 'ProjectList';

export default ProjectList;