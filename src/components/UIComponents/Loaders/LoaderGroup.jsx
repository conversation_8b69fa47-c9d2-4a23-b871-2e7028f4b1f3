import React from 'react';

const ProjectListSkeleton = ({ theme = 'light' }) => {
  // Theme classes
  const themeClasses = {
    light: {
      container: "bg-custom-bg-primary h-[calc(100dvh-12rem)] md:h-[88%] flex flex-col overflow-y-hidden",
      headerBorder: "border-custom-border",
      skeletonBg: "bg-semantic-gray-100",
      itemBg: "bg-semantic-gray-50",
      itemBorder: "border-custom-border"
    },
    dark: {
      container: "bg-custom-bg-primary h-[calc(100dvh-12rem)] md:h-[88%] flex flex-col overflow-y-hidden",
      headerBorder: "border-custom-border",
      skeletonBg: "bg-semantic-gray-700",
      itemBg: "bg-semantic-gray-800",
      itemBorder: "border-custom-border"
    }
  };

  const currentTheme = themeClasses[theme] || themeClasses.light;

  return (
    <div className={currentTheme.container}>
      {/* Search Input Loading */}
      <div className={`flex items-center justify-between px-4 py-3 border-b ${currentTheme.headerBorder}`}>
        <div className={`h-4 w-64 ${currentTheme.skeletonBg} rounded-lg animate-pulse`}></div>
        <div className={`h-6 w-16 ${currentTheme.skeletonBg} rounded-lg animate-pulse`}></div>
      </div>

      {/* Project Item Loading */}
      <div className="space-y-4 px-4 py-4">
        {[...Array(7)].map((_, i) => (
          <div
            key={i}
            className={`flex items-center space-x-4 p-4 ${currentTheme.itemBg} border ${currentTheme.itemBorder} rounded-lg animate-pulse`}
          >
            <div className="flex-1">
              <div className={`h-4 w-full ${currentTheme.skeletonBg} rounded-lg mb-2`}></div>
              <div className={`h-3 w-32 ${currentTheme.skeletonBg} rounded-lg`}></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const ChatListSkeleton = () => {
  return (
    <div className="bg-white h-full flex flex-col">
      {/* Search Input Loading */}
      <div className="flex flex-col px-6 pt-4">
        <div className="h-4 w-64 bg-gray-100 animate-pulse rounded-lg"></div> 
        <div className="border-b border-gray-200 mt-4"></div> 
      </div>

      {/* Chat Item Loading */}
      <div className="space-y-4 mt-6 px-6">
        {[...Array(7)].map((_, i) => (
          <div
            key={i}
            className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg animate-pulse" 
          >
            <div className="flex-1">
              <div className="h-4 w-3/4 bg-gray-200 rounded-lg mb-2"></div> 
              <div className="h-3 w-1/4 bg-gray-200 rounded-lg"></div> 
            </div>
            <div className="h-6 w-6 bg-gray-200 rounded-lg"></div> 
          </div>
        ))}
      </div>
    </div>
  );
};

const TableLoadingSkeleton = () => {
  return (
    <div className="w-full flex flex-col overflow-hidden"> 
    {/* Header with Search Input and Action Buttons */}
    <div className="flex justify-between items-center">
      {/* Left Search Input */}
      <div className="relative flex items-center w-64">
        <div className="animate-pulse w-full">
          <div className="bg-gray-100 h-6 rounded"></div>
        </div>
      </div>
  
      {/* Right Action Buttons */}
      <div className="flex space-x-4">
        <div className="animate-pulse">
          <div className="bg-gray-100 h-10 w-24 rounded-lg"></div> 
        </div>
        <div className="animate-pulse">
          <div className="bg-gray-100 h-10 w-24 rounded-lg"></div> 
        </div>
      </div>
    </div>
  
    {/* Table Skeleton with Outline and Padding */}
    <div className="border p-4 mt-4 rounded-lg"> 
      {/* Table Header with border-bottom */}
      <div className="grid grid-cols-5 gap-6 mb-4 border-b pb-4"> 
        {['w-32', 'w-48', 'w-36', 'w-40', 'w-24'].map((width, index) => (
          <div key={index} className="flex items-center gap-2">
            <div className={`h-5 bg-gray-100 rounded-md ${width}`}></div>
          </div>
        ))}
      </div>
  
      {/* Reduced Table Rows with border-bottom */}
      {[...Array(5)].map((_, rowIndex) => (
        <div key={rowIndex} className="grid grid-cols-5 gap-6 py-4 border-b"> 
          <div className="h-5 bg-gray-100 rounded-md w-32"></div>
          <div className="h-5 bg-gray-100 rounded-md w-48"></div>
          <div className="h-5 bg-gray-100 rounded-md w-36"></div>
          <div className="h-5 bg-gray-100 rounded-md w-40"></div>
          <div className="flex justify-between items-center">
            <div className="h-6 bg-gray-100 rounded-full w-16"></div>
          </div>
        </div>
      ))}
  
      {/* Table Footer inside the table */}
      <div className="flex justify-between items-center mt-4 border-t pt-4"> 
        <div className="flex items-center gap-6"> 
          <div className="h-8 bg-gray-100 rounded-md w-32"></div>
          <div className="h-8 bg-gray-100 rounded-md w-16"></div>
        </div>
        <div className="flex gap-6"> 
          <div className="h-8 w-8 bg-gray-100 rounded-md"></div>
          <div className="h-8 w-8 bg-gray-100 rounded-md"></div>
          <div className="h-8 w-8 bg-gray-100 rounded-md"></div>
        </div>
      </div>
    </div>
  </div>
  
  );
};

const CardLoadingSkeleton = () => {
  return (
    <div className="w-full p-4">
  <div className="bg-white rounded-lg overflow-hidden border border-gray-200">
    {/* Card Header */}
    <div className="p-6 flex items-center space-x-4">
      {/* Bookmark Icon */}
      <div className="animate-pulse">
        <div className="bg-gray-100 w-10 h-10 rounded-full"></div>
      </div>

      {/* Title */}
      <div className="animate-pulse flex-grow">
        <div className="bg-gray-100 h-6 w-1/2 rounded-lg"></div>
      </div>
    </div>

    {/* Horizontal Line */}
    <div className="border-t border-gray-200 mx-6"></div>

    {/* Data Rows */}
    <div className="p-6 grid grid-cols-3 gap-6">
      {[...Array(3)].map((_, index) => (
        <div key={index} className="animate-pulse space-y-2">
          <div className="bg-gray-50 h-4 w-3/4 rounded-lg"></div>
          <div className="bg-gray-50 h-4 w-1/2 rounded-lg"></div>
        </div>
      ))}
    </div>

    {/* Accordion Skeleton */}
    <div className="p-6 border-t border-gray-200">
      <div className="animate-pulse">
        {/* Accordion Header */}
        <div className="bg-gray-100 h-10 w-full rounded-lg mb-4"></div>

        {/* Accordion Content */}
        <div className="space-y-3">
          {[...Array(3)].map((_, index) => (
            <div key={index} className="bg-gray-50 h-4 w-full rounded-lg"></div>
          ))}
        </div>
      </div>
    </div>

    {/* Final Line Text */}
    <div className="p-6 border-t border-gray-200">
      <div className="animate-pulse">
        <div className="bg-gray-50 h-4 w-full rounded-lg"></div>
      </div>
    </div>
  </div>
</div>
  );
};

const RequirementCardLoadingSkeleton = () => {
  return (
    <div className="w-full p-4">
      {/* Breadcrumb */}
      <div className="mb-4 animate-pulse">
        <div className="bg-gray-200 h-4 w-1/3 rounded"></div>
      </div>

      <div className="bg-white shadow-lg rounded-lg overflow-hidden border border-gray-200">
        {/* Card Header */}
        <div className="p-6 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* Bookmark Icon */}
            <div className="animate-pulse">
              <div className="bg-gray-300 w-10 h-10 rounded-full"></div>
            </div>

            {/* Title */}
            <div className="flex items-center space-x-4">
              <div className="animate-pulse flex-grow">
                <div className="bg-gray-300 h-6 w-48 rounded"></div>
              </div>

              {/* Badge */}
              <div className="animate-pulse">
                <div className="bg-gray-200 h-5 w-16 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Horizontal Line */}
        <div className="border-t border-gray-200 mx-6"></div>

        {/* Data Rows */}
        <div className="p-6 grid grid-cols-3 gap-6">
          {[...Array(3)].map((_, index) => (
            <div key={index} className="animate-pulse space-y-2">
              <div className="bg-gray-200 h-4 w-3/4 rounded"></div>
              <div className="bg-gray-200 h-4 w-1/2 rounded"></div>
            </div>
          ))}
        </div>

        {/* Accordion Skeleton */}
        <div className="p-6">
          <div className="animate-pulse">
            {/* Accordion Header */}
            <div className="bg-gray-300 h-10 w-full rounded mb-4"></div>

            {/* Accordion Content */}
            <div className="space-y-3">
              {[...Array(3)].map((_, index) => (
                <div key={index} className="bg-gray-200 h-4 w-full rounded"></div>
              ))}
            </div>
          </div>
        </div>

        {/* Final Line Text */}
        <div className="p-6 border-t border-gray-200">
          <div className="animate-pulse">
            <div className="bg-gray-200 h-4 w-full rounded"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

const UIUXLoadingSkeleton = () => {
  return (
    <>
      <div className='w-full flex items-center justify-between p-4 border-b border-gray-300 mb-4'>
        {/*sticky header */}
        <div className='w-32 animate-pulse h-6 bg-gray-200 '></div>
        <div className='flex animate-pulse gap-6'>
          <div className='w-32 h-6  bg-gray-200'></div>
          <div className='w-32 h-6 bg-gray-200'></div>
        </div>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-3 md:grid-cols-2 gap-4">
        {[...Array(9)].map((_, index) => (
          <div key={index} className="bg-white rounded-lg border border-gray-200 p-4 animate-pulse">
            <div className="space-y-3">
              {/* Header section */}
              <div className="flex justify-between items-start">
                <div className="space-y-2">
                  <div className="h-6 bg-gray-300 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
                  <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
                  <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
                </div>
              </div>

              {/* Status section */}
              <div className="space-y-2">
                <div className="flex justify-between">
                  <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="h-2 bg-gray-300 rounded-full w-3/4"></div>
                </div>
              </div>

              {/* Footer section */}
              <div className="flex justify-between items-center pt-2">
                <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </>
  );
};

const ProfileDetailsLoadingSkeleton = () => {
  return (
    <div className="w-full p-4">
      {/* Top Heading */}
      <div className="flex flex-col items-start mb-4">
        <div className="animate-pulse w-full mb-2">
          <div className="bg-gray-100 h-8 w-3/4 rounded-lg"></div>
        </div>

        {/* Small Text */}
        <div className="animate-pulse w-full">
          <div className="bg-gray-100 h-4 w-1/2 rounded-lg"></div>
        </div>
      </div>

      {/* Profile Section */}
      <div className="flex items-center justify-center space-x-6 mb-6">
        {/* Profile Image */}
        <div className="animate-pulse">
          <div className="bg-gray-100 w-24 h-24 rounded-full"></div>
        </div>

        {/* Two-Column Text */}
        <div className="grid grid-cols-2 gap-4 flex-grow">
          <div className="animate-pulse">
            <div className="bg-gray-100 h-4 w-full rounded-lg mb-2"></div>
            <div className="bg-gray-100 h-4 w-3/4 rounded-lg"></div>
          </div>
        </div>
      </div>

      {/* Profile Details Card */}
      <div className="bg-white border border-gray-100 p-6 w-full rounded-lg">
        {/* Card Heading */}
        <div className="animate-pulse mb-6">
          <div className="bg-gray-100 h-6 w-1/2 rounded-lg"></div>
        </div>

        {/* Name Row */}
        <div className="flex space-x-4 mb-6">
          <div className="flex-1 animate-pulse">
            <div className="bg-gray-100 h-4 w-full rounded-lg"></div>
          </div>
          <div className="flex-1 animate-pulse">
            <div className="bg-gray-100 h-4 w-full rounded-lg"></div>
          </div>
        </div>

        {/* Email */}
        <div className="animate-pulse mb-6">
          <div className="bg-gray-100 h-4 w-full rounded-lg"></div>
        </div>

        {/* Designation and Department */}
        <div className="flex space-x-4 mb-6">
          <div className="flex-1 animate-pulse">
            <div className="bg-gray-100 h-4 w-full rounded-lg"></div>
          </div>
          <div className="flex-1 animate-pulse">
            <div className="bg-gray-100 h-4 w-full rounded-lg"></div>
          </div>
        </div>

        {/* Time Zone */}
        <div className="animate-pulse mb-6">
          <div className="bg-gray-100 h-4 w-full rounded-lg"></div>
        </div>

        {/* Active User */}
        <div className="animate-pulse mb-6">
          <div className="bg-gray-100 h-4 w-1/2 rounded-lg"></div>
        </div>
      </div>

      {/* Button at Right Side */}
      <div className="flex justify-end mt-6">
        <div className="animate-pulse">
          <div className="bg-gray-100 h-10 w-32 rounded-lg"></div>
        </div>
      </div>
    </div>
  );
};
const LLMSettingsSkeleton = ({ theme = 'light' }) => {
  const themeClasses = {
    light: {
      skeletonBg: 'bg-semantic-gray-100'
    },
    dark: {
      skeletonBg: 'bg-semantic-gray-700'
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-6">
      {/* Main Title */}
      <div className="animate-pulse mb-8">
        <div className={`${themeClasses[theme].skeletonBg} h-8 w-1/2 rounded-lg`}></div>
      </div>

      {/* Four Sections */}
      {[...Array(4)].map((_, index) => (
        <div key={index} className="mb-6">
          {/* Subtitle with increased width */}
          <div className="animate-pulse mb-4">
            <div className={`${themeClasses[theme].skeletonBg} h-5 w-2/3 rounded-lg`}></div>
          </div>

          {/* Select Tag Placeholder with reduced width */}
          <div className="animate-pulse">
            <div className={`${themeClasses[theme].skeletonBg} h-8 w-1/3 rounded-lg`}></div>
          </div>
        </div>
      ))}
    </div>
  );
};

const NotificationLoadingSkeleton = ({ theme = 'light' }) => {
  const themeClasses = {
    light: {
      container: 'bg-custom-bg-primary border-custom-border',
      placeholder: 'bg-semantic-gray-300',
      textPlaceholder: 'bg-semantic-gray-200',
    },
    dark: {
      container: 'bg-custom-bg-primary border-custom-border',
      placeholder: 'bg-semantic-gray-600',
      textPlaceholder: 'bg-semantic-gray-700',
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-4 space-y-6">
      {[...Array(3)].map((_, index) => (
        <div key={index} className={`shadow-md rounded-lg p-6 border ${themeClasses[theme].container}`}>
          {/* Top Row: Delete Icon + Text */}
          <div className="flex items-center space-x-4 mb-4">
            {/* Delete Icon Placeholder */}
            <div className="animate-pulse">
              <div className={`h-10 w-10 rounded-full ${themeClasses[theme].placeholder}`}></div>
            </div>
            {/* Text Placeholder */}
            <div className="flex-1 animate-pulse">
              <div className={`h-5 w-2/3 rounded ${themeClasses[theme].textPlaceholder}`}></div>
            </div>
          </div>

          {/* Text Rows */}
          <div className="space-y-3">
            {/* Row 1 */}
            <div className="animate-pulse">
              <div className={`h-4 w-full rounded ${themeClasses[theme].textPlaceholder}`}></div>
            </div>
            {/* Row 2 */}
            <div className="animate-pulse">
              <div className={`h-4 w-3/4 rounded ${themeClasses[theme].textPlaceholder}`}></div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

const TwoColumnLoadingSkeleton = () => {
  return (
    <div className="flex gap-4 p-4">
      <div className="w-1/4 pr-2 border-r">
        <div className="h-6 bg-gray-200 animate-pulse rounded mb-4 w-1/2"></div>
        <div className="h-5 bg-gray-200 animate-pulse rounded mb-4"></div>
        <div className="h-5 bg-gray-200 animate-pulse rounded mb-4 w-3/4"></div>
        <div className="h-5 bg-gray-200 animate-pulse rounded mb-4 w-4/5"></div>
        <div className="h-5 bg-gray-200 animate-pulse rounded mb-4 w-3/4"></div>
        <div className="h-5 bg-gray-200 animate-pulse rounded mb-4"></div>
        <div className="h-5 bg-gray-200 animate-pulse rounded mb-4 w-4/5"></div>
        <div className="h-5 bg-gray-200 animate-pulse rounded mb-4 w-3/4"></div>
        <div className="h-5 bg-gray-200 animate-pulse rounded mb-4"></div>
        <div className="h-5 bg-gray-200 animate-pulse rounded mb-4 w-4/5"></div>
        <div className="h-5 bg-gray-200 animate-pulse rounded mb-4"></div>
        <div className="h-5 bg-gray-200 animate-pulse rounded mb-4 w-3/4"></div>
        <div className="h-5 bg-gray-200 animate-pulse rounded mb-4 w-4/5"></div>
        <div className="h-5 bg-gray-200 animate-pulse rounded"></div> 
      </div>

      {/* Right Column - Single Card + Accordions */}
      <div className="flex-1 space-y-6">
        {/* <div className="bg-white shadow-md rounded-lg p-5 border border-gray-100 animate-pulse">
          <div className="bg-gray-200 h-7 w-1/2 rounded-md mb-4"></div>
          <div className="space-y-2">
            <div className="bg-gray-200 h-5 w-full rounded-md"></div>
            <div className="bg-gray-200 h-5 w-2/3 rounded-md"></div>
          </div>
        </div> */}
        <div className="w-full border rounded-lg p-4 bg-white animate-pulse">
          {/* Header */}
          <div className="flex items-center justify-between mb-4 shadow-md">
            <div className="h-6 bg-gray-300 rounded w-1/4"></div>
            <div className="flex gap-2">
              <div className="h-8 bg-gray-300 rounded w-24"></div>
              <div className="h-8 bg-gray-300 rounded w-32"></div>
            </div>
          </div>

          {/* Progress Bars */}
          <div className="grid grid-cols-4 gap-4 mb-6 pt-4 border-t">
            {Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="flex flex-col items-center">
                <div className="h-4 bg-gray-300 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-300 rounded w-full"></div>
              </div>
            ))}
          </div>

          {/* Details */}
          <div className="w-full border-t pt-4">
            <div className="h-4 bg-gray-300 rounded w-1/3 mb-4"></div>
            <div className="space-y-2">
              {Array.from({ length: 3 }).map((_, index) => (
                <div key={index} className="flex justify-between">
                  <div className="h-4 bg-gray-300 rounded w-1/3"></div>
                  <div className="h-4 bg-gray-300 rounded w-16"></div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Five Accordions (Simple Divs) */}
        <div className="space-y-4">
          <div
            className="bg-white shadow-sm rounded-lg p-4 py-3 border border-gray-100 animate-pulse"
          >
            {/* Accordion Header */}
            <div className="flex items-center justify-between">
              <div className="bg-gray-200 h-6 w-3/4 rounded-md"></div>
              <div className="bg-gray-200 h-5 w-8 rounded-full"></div>
            </div>
          </div>
          <div
            className="bg-white shadow-sm rounded-lg p-4 border border-gray-100 animate-pulse"
          >
            {/* Accordion Header */}
            <div className="flex items-center justify-between mb-3">
              <div className="bg-gray-200 h-6 w-3/4 rounded-md"></div>
              <div className="bg-gray-200 h-5 w-8 rounded-full"></div>
            </div>

            {/* Accordion Content */}
            <div className="space-y-2">
              <div className="bg-gray-200 h-4 w-full rounded-md"></div>
            </div>
          </div>
          <div
            className="bg-white shadow-sm rounded-lg p-4 border border-gray-100 animate-pulse"
          >
            {/* Accordion Header */}
            <div className="flex items-center justify-between mb-3">
              <div className="bg-gray-200 h-6 w-3/4 rounded-md"></div>
              <div className="bg-gray-200 h-5 w-8 rounded-full"></div>
            </div>

            {/* Accordion Content */}
            <div className="space-y-2">
              <div className="bg-gray-200 h-4 w-full rounded-md"></div>
              <div className="bg-gray-200 h-4 w-3/4 rounded-md"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const TwoColumnSkeletonLoader = () => {
  return (
    <div className="flex gap-4 p-4">
      <div className="w-1/4 pr-2 border-r">
        <div className="h-5 bg-gray-100 animate-pulse rounded-lg mb-4 w-1/2"></div>
        <div className="h-4 bg-gray-100 animate-pulse rounded-lg mb-4"></div>
        <div className="h-4 bg-gray-100 animate-pulse rounded-lg mb-4 w-3/4"></div>
        <div className="h-4 bg-gray-100 animate-pulse rounded-lg mb-4 w-4/5"></div>
        <div className="h-4 bg-gray-100 animate-pulse rounded-lg mb-4 w-3/4"></div>
        <div className="h-4 bg-gray-100 animate-pulse rounded-lg mb-4"></div>
        <div className="h-4 bg-gray-100 animate-pulse rounded-lg mb-4 w-4/5"></div>
        <div className="h-4 bg-gray-100 animate-pulse rounded-lg mb-4 w-3/4"></div>
        <div className="h-4 bg-gray-100 animate-pulse rounded-lg mb-4"></div>
        <div className="h-4 bg-gray-100 animate-pulse rounded-lg mb-4 w-4/5"></div>
        <div className="h-4 bg-gray-100 animate-pulse rounded-lg mb-4"></div>
        <div className="h-4 bg-gray-100 animate-pulse rounded-lg mb-4 w-3/4"></div>
        <div className="h-4 bg-gray-100 animate-pulse rounded-lg mb-4 w-4/5"></div>
        <div className="h-4 bg-gray-100 animate-pulse rounded-lg"></div>
      </div>

      <div className="p-4 space-y-6 flex-1">
        <div className="border border-gray-200 flex p-4 flex-col space-y-3">
          <div className="h-6 bg-gray-100 animate-pulse rounded-lg w-2/3"></div>
          <div className="flex items-center justify-between">
            <div className="h-5 bg-gray-100 animate-pulse rounded-lg w-1/3"></div>
            <div className="h-8 bg-gray-100 animate-pulse rounded-lg w-1/4"></div>
          </div>
        </div>

        <div className="border border-gray-200 rounded-lg p-4 space-y-3">
          <div className="h-5 bg-gray-100 animate-pulse rounded-lg w-1/4"></div>
          <div className="h-3 bg-gray-100 animate-pulse rounded-lg w-full"></div>
          <div className="h-3 bg-gray-100 animate-pulse rounded-lg w-5/6"></div>
          <div className="h-3 bg-gray-100 animate-pulse rounded-lg w-4/6"></div>
        </div>

        <div className="border border-gray-200 rounded-lg p-4 space-y-3">
          <div className="h-5 bg-gray-100 animate-pulse rounded-lg w-1/4"></div>
          <div className="h-3 bg-gray-100 animate-pulse rounded-lg w-full"></div>
          <div className="h-3 bg-gray-100 animate-pulse rounded-lg w-5/6"></div>
          <div className="h-3 bg-gray-100 animate-pulse rounded-lg w-4/6"></div>
        </div>

           <div className="border border-gray-200 rounded-lg p-4 space-y-3">
          <div className="h-5 bg-gray-100 animate-pulse rounded-lg w-1/4"></div>
          <div className="h-3 bg-gray-100 animate-pulse rounded-lg w-full"></div>
          <div className="h-3 bg-gray-100 animate-pulse rounded-lg w-5/6"></div>
          <div className="h-3 bg-gray-100 animate-pulse rounded-lg w-4/6"></div>
        </div>
      </div>
    </div>
  );
};

const LayoutWithPlaceholders = () => {
  return (
    <div className="flex gap-4 p-4">
      {/* Left Column - Content Section */}
      <div className="w-1/3 pr-2 border-r space-y-4">
        {/* New Card with 3 Buttons (Column-Wise) */}
        <div className="bg-white shadow-md rounded-lg p-4 border border-gray-100">
          <div className="flex flex-col space-y-3">
            <button className="bg-gray-300 h-8 w-full rounded-md animate-pulse"></button>
            <button className="bg-gray-300 h-8 w-full rounded-md animate-pulse"></button>
            <button className="bg-gray-300 h-8 w-full rounded-md animate-pulse"></button>
          </div>
        </div>
        <div className="h-6 bg-gray-200 animate-pulse rounded mb-4 w-1/2"></div>
        <div className="h-5 bg-gray-200 animate-pulse rounded mb-4"></div>
        <div className="h-5 bg-gray-200 animate-pulse rounded mb-4 w-3/4"></div>
        <div className="h-5 bg-gray-200 animate-pulse rounded mb-4 w-4/5"></div>
        <div className="h-5 bg-gray-200 animate-pulse rounded mb-4 w-3/4"></div>
        <div className="h-5 bg-gray-200 animate-pulse rounded mb-4"></div>
        <div className="h-5 bg-gray-200 animate-pulse rounded mb-4 w-4/5"></div>
        <div className="h-5 bg-gray-200 animate-pulse rounded mb-4 w-3/4"></div>
        <div className="h-5 bg-gray-200 animate-pulse rounded mb-4"></div>
        <div className="h-5 bg-gray-200 animate-pulse rounded"></div>

        {/* <div className="animate-pulse">
          <div className="bg-gray-200 h-10 w-2/3 rounded-md shadow-sm"></div>
        </div>

        <div className="space-y-4">
          {[...Array(8)].map((_, index) => (
            <div
              key={index}
              className="animate-pulse flex items-center space-x-3"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="bg-gray-200 h-4 w-4 rounded-full"></div>
              <div className="bg-gray-200 h-6 w-3/4 rounded-md flex-grow"></div>
            </div>
          ))}
      </div> */}
      </div>

      {/* Right Column - Single Card + Accordions */}
      <div className="w-2/3 space-y-6">
        {/* Single Nested Card Placeholder with Content */}
        <div className="bg-white shadow-md rounded-lg p-5 border border-gray-100">
          {/* Title and Button Row */}
          <div className="flex items-center justify-between mb-4">
            <div className="bg-gray-200 h-7 w-1/4 rounded-md"></div>
            <div className="bg-gray-200 h-6 w-16 rounded-md"></div>
          </div>

          {/* Card Content */}
          <div className="space-y-3">
            <p className="bg-gray-200 h-4 w-full rounded-md animate-pulse"></p>
            <p className="bg-gray-200 h-4 w-3/4 rounded-md animate-pulse"></p>
            <p className="bg-gray-200 h-4 w-5/6 rounded-md animate-pulse"></p>
            <p className="bg-gray-200 h-4 w-2/3 rounded-md animate-pulse"></p>
          </div>

          {/* Bottom Right Button */}
          <div className="flex justify-end mt-4">
            <div className="bg-gray-200 h-6 w-24 rounded-md"></div>
          </div>
        </div>

        {/* Five Accordions (Simple Divs) */}
        <div className="space-y-4">
          {[...Array(5)].map((_, index) => (
            <div
              key={index}
              className="bg-white shadow-sm rounded-lg p-4 border border-gray-100"
              style={{ animationDelay: `${index * 0.15}s` }}
            >
              {/* Accordion Header */}
              <div className="flex items-center justify-between mb-3">
                <div className="bg-gray-200 h-6 w-3/4 rounded-md"></div>
                <div className="flex space-x-2">
                  {/* 3 Buttons */}
                  <button className="bg-gray-300 h-6 w-8 rounded-md animate-pulse"></button>
                  <button className="bg-gray-300 h-6 w-8 rounded-md animate-pulse"></button>
                  <button className="bg-gray-300 h-6 w-8 rounded-md animate-pulse"></button>
                </div>
              </div>

              {/* Accordion Content */}
              <div className="space-y-2">
                <div className="bg-gray-200 h-4 w-full rounded-md"></div>
                <div className="bg-gray-200 h-4 w-3/4 rounded-md"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div >
  );
};

const ScmRepoLoader = ()=>{
  return (
    <div className="w-full flex flex-col p-4 overflow-hidden"> 
    {/* Header with Search Input and Action Buttons */}
    <div className="flex justify-between items-center p-4">
      {/* Left Search Input */}
      <div className="relative flex items-center w-64">
        <div className="animate-pulse w-full">
          <div className="bg-gray-100 h-6 rounded"></div>
        </div>
      </div>
  
      {/* Right Action Buttons */}
      <div className="flex space-x-4">
        <div className="animate-pulse">
          <div className="bg-gray-100 h-10 w-24 rounded-lg"></div> 
        </div>
      </div>
    </div>
  
    {/* Table Skeleton with Outline and Padding */}
    <div className="border p-4 mt-4 rounded-lg"> 
      {/* Table Header with border-bottom */}
      <div className="grid grid-cols-5 gap-6 mb-4 border-b pb-4"> 
        {['w-32', 'w-48', 'w-36', 'w-40', 'w-24'].map((width, index) => (
          <div key={index} className="flex items-center gap-2">
            <div className={`h-5 bg-gray-100 rounded-md ${width}`}></div>
          </div>
        ))}
      </div>
  
      {/* Reduced Table Rows with border-bottom */}
      {[...Array(5)].map((_, rowIndex) => (
        <div key={rowIndex} className="grid grid-cols-5 gap-6 py-4 border-b"> 
          <div className="h-5 bg-gray-100 rounded-md w-32"></div>
          <div className="h-5 bg-gray-100 rounded-md w-48"></div>
          <div className="h-5 bg-gray-100 rounded-md w-36"></div>
          <div className="h-5 bg-gray-100 rounded-md w-40"></div>
          <div className="flex justify-between items-center">
            <div className="h-6 bg-gray-100 rounded-full w-16"></div>
          </div>
        </div>
      ))}
  
      {/* Table Footer inside the table */}
      <div className="flex justify-between items-center mt-4 border-t pt-4"> 
        <div className="flex items-center gap-6"> 
          <div className="h-8 bg-gray-100 rounded-md w-32"></div>
          <div className="h-8 bg-gray-100 rounded-md w-16"></div>
        </div>
        <div className="flex gap-6"> 
          <div className="h-8 w-8 bg-gray-100 rounded-md"></div>
          <div className="h-8 w-8 bg-gray-100 rounded-md"></div>
          <div className="h-8 w-8 bg-gray-100 rounded-md"></div>
        </div>
      </div>
    </div>
  </div>
  
  );
}

const PulseAnimateLoader = () => {
  return (
    <span className="typography-caption bg-gray-100 text-gray-700 px-2 py-1 rounded-full flex items-center space-x-1">
      <span className="inline-flex">
        <span className="h-1 w-1 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></span>
        <span className="h-1 w-1 bg-gray-500 rounded-full animate-bounce mx-1" style={{ animationDelay: '150ms' }}></span>
        <span className="h-1 w-1 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></span>
      </span>
    </span>
  );
}

const NotificationLoader = ({ theme = 'light' }) => {
  const themeClasses = {
    light: {
      container: 'border-custom-border',
      titleBg: 'bg-semantic-gray-100',
      badgeBg: 'bg-semantic-gray-100',
      cardBg: 'border-custom-border',
      textBg: 'bg-semantic-gray-100',
    },
    dark: {
      container: 'border border-custom-border',
      titleBg: 'bg-semantic-gray-700',
      badgeBg: 'bg-semantic-gray-700',
      cardBg: 'border border-custom-border bg-custom-bg-primary',
      textBg: 'bg-semantic-gray-700',
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-4 space-y-4">
      {/* Title and Badge */}
      <div className="flex items-center gap-2">
        <div className={`h-8 rounded-xl w-48 animate-pulse ${themeClasses[theme].titleBg}`}></div>
        <div className={`h-6 rounded-full w-20 animate-pulse ${themeClasses[theme].badgeBg}`}></div>
      </div>

      {/* First Card */}
      <div className={`rounded-xl p-4 space-y-4 ${themeClasses[theme].cardBg}`}>
        <div className={`h-6 rounded-xl w-36 animate-pulse ${themeClasses[theme].textBg}`}></div>
        
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <div className={`h-4 rounded-xl w-24 animate-pulse ${themeClasses[theme].textBg}`}></div>
            <div className={`h-8 rounded-xl w-20 animate-pulse ${themeClasses[theme].textBg}`}></div>
          </div>
          <div className="flex justify-between items-center">
            <div className={`h-4 rounded-xl w-32 animate-pulse ${themeClasses[theme].textBg}`}></div>
            <div className={`h-8 rounded-xl w-20 animate-pulse ${themeClasses[theme].textBg}`}></div>
          </div>
        </div>
      </div>

      {/* Second Card */}
      <div className={`rounded-xl p-4 ${themeClasses[theme].cardBg}`}>
        <div className="flex justify-between items-center mb-4">
          <div className={`h-6 rounded-xl w-32 animate-pulse ${themeClasses[theme].textBg}`}></div>
          <div className={`h-6 rounded-full w-16 animate-pulse ${themeClasses[theme].textBg}`}></div>
        </div>
        
        <div className={`rounded-xl p-3 space-y-2 ${themeClasses[theme].container}`}>
          <div className={`h-4 rounded-xl w-full animate-pulse ${themeClasses[theme].textBg}`}></div>
          <div className={`h-4 rounded-xl w-3/4 animate-pulse ${themeClasses[theme].textBg}`}></div>
        </div>
      </div>
    </div>
  );
};

export {
  ProjectListSkeleton,
  ChatListSkeleton,
  TableLoadingSkeleton,
  CardLoadingSkeleton,
  RequirementCardLoadingSkeleton,
  UIUXLoadingSkeleton,
  ProfileDetailsLoadingSkeleton,
  LLMSettingsSkeleton,
  NotificationLoadingSkeleton,
  TwoColumnLoadingSkeleton,
  TwoColumnSkeletonLoader,
  LayoutWithPlaceholders,
  ScmRepoLoader,
  NotificationLoader,
  PulseAnimateLoader
};